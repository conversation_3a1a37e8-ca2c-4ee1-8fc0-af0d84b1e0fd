<template>
    <div class="ratio-chart-wrap">
        <div class="score-wrap" v-if="score" :style="{ width: `${score}%` }">
            <p class="score-text">{{ score }}</p>
        </div>
        <div class="ratio-chart-bar-bg" :class="{ 'show-subgroup-info': showSubgroupInfo }">
            <template v-for="(item, index) in subgroupConfig" :key="index">
                <div class="ratio-chart-bar-item" :style="{ width: `${item.width}%`, backgroundColor: item.color }">
                    <p v-if="showSubgroupInfo && index < subgroupConfig.length - 1" class="subgroup-info">{{ subgroup[index] }}%</p>
                </div>
            </template>
        </div>
    </div>
</template>
<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps({
    score: {
        type: Number, // 百分比得分
        default: 30,
    },
    subgroup: {
        type: Array, // 分组
        default: () => [10, 30, 70, 90],
    },
    colorTheme: {
        type: String, // 颜色主题
        default: 'default',
    },
    showSubgroupInfo: {
        type: Boolean, // 是否显示分组信息
        default: true,
    },
});

const colorThemeMap: Record<string, string[]> = {
    default: ['#16B3B3', '#78D6D6', '#A3EBEB'],
    purple: ['#7046FA', 'rgba(112,70,250, 0.6)', 'rgba(112,70,250, 0.3)'],
    orange: ['rgba(255,120,71, 1)', 'rgba(255,120,71, 0.6)', 'rgba(255,120,71, 0.3)'],
    blue: ['rgba(18,161,251, 1)', 'rgba(18,161,251, 0.6)', 'rgba(18,161,251, 0.3)'],
    green: ['rgba(103,204,53, 1)', 'rgba(103,204,53, 0.6)', 'rgba(103,204,53, 0.3)'],
};

const colorThemeConfig = computed(() => {
    const colorThemeConfig = [...colorThemeMap[props.colorTheme || 'default']];
    const reverseColorThemeConfig = [...colorThemeMap[props.colorTheme || 'default']];
    reverseColorThemeConfig.splice(0, 1);
    reverseColorThemeConfig.splice(reverseColorThemeConfig.length - 1, 1);
    const list = colorThemeConfig.concat(reverseColorThemeConfig);
    return list;
});

const subgroupConfig = computed(() => {
    const config: { width: number; color: string }[] = [];
    const subgroupList = props.subgroup.concat(100); // 添加100作为最后一个分组
    const cConfigL = colorThemeConfig.value.length;
    let colorIndex = 0;

    subgroupList.forEach((item: any, index: number) => {
        const color = colorThemeConfig.value[colorIndex];

        if (colorIndex < cConfigL - 1) {
            colorIndex += 1;
        } else {
            colorIndex = 0;
        }

        if (index === 0) {
            config.push({
                width: item,
                color,
            });
        } else {
            config.push({
                width: item - (subgroupList[index - 1] as number),
                color,
            });
        }
    });
    return config;
});
</script>
<style lang="less" scoped>
.ratio-chart-wrap {
    .score-wrap {
        text-align: right;
        position: relative;
        top: 0;
        left: 0;
        height: 24px;

        .score-text {
            position: absolute;
            width: 25px;
            height: 14px;
            text-align: center;
            top: 0;
            right: 0;
            transform: translateX(50%);
            color: #363f55;
            font-family: var(--FZLanTingHeiS-R-GB);
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 14px;
            text-align: center;

            &:after {
                content: '';
                position: absolute;
                top: 14px;
                right: 0;
                width: 25px;
                height: 10px;
                background-image: url('https://img.bosszhipin.com/static/zhipin/kanjian/evaluate/684927974257111324.png');
                background-size: auto 10px;
                background-repeat: no-repeat;
                background-position: center;
            }
        }
    }

    .ratio-chart-bar-bg {
        display: flex;

        &.show-subgroup-info {
            padding-bottom: 15px;
        }

        .ratio-chart-bar-item {
            height: 10px;
            border-radius: 10px;
            position: relative;

            .subgroup-info {
                position: absolute;
                top: 10px;
                right: 0;
                transform: translateX(50%);
                color: #9fa6b5;
                font-size: 10px;
                font-style: normal;
                font-weight: 400;
                line-height: 18px;
                text-align: center;
            }
        }
    }
}
</style>
