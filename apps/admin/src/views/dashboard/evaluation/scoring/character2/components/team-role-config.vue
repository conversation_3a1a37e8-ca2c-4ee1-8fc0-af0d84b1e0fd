<template>
    <div class="team-role-config">
        <b-form ref="formRef" :model="{ configGroups, weightTableData, normTableData }" layout="vertical" :scrollToFirstError="{ block: 'center' }">
            <div class="sub-title">
                <h4>团队角色分数计算</h4>
            </div>

            <!-- 团队角色分数权重表 -->
            <div class="section-title">
                <h5>团队角色分数权重表</h5>
            </div>

            <b-table border fitWidth :columns="weightColumns" :tableData="weightTableData">
                <template v-for="(col, index) in weightColumns" :key="col.field" #[`th-${col.field}`]>
                    <div v-if="index === 0" class="reference">
                        <div style="white-space: nowrap">团队角色名称</div>
                    </div>
                    <div v-else class="reference">
                        <FormField :field="`configGroups.${index - 1}.name`" hideLabel :rules="getConfigGroupNameRules()">
                            <b-input v-model.trim="configGroups[index - 1].name" placeholder="请输入配置组名称" :maxLength="10" />
                        </FormField>
                        <!-- 团队角色组件不显示删除按钮，因为配置组固定 -->
                    </div>
                </template>
                <template v-for="(col, index) in weightColumns" :key="col.field" #[`td-${col.field}`]="{ raw, $index }">
                    <div v-if="index === 0">
                        {{ raw.dimensionName }}
                    </div>
                    <div v-else>
                        <FormField :field="`weightTableData.${$index}.${col.field}`" hideLabel :rules="getWeightRules($index, col.field)">
                            <b-input-number
                                v-model="raw[col.field]"
                                placeholder="请输入权重"
                                hideButton
                                :min="CHARACTER2_CONSTANTS.WEIGHT_MIN"
                                :max="CHARACTER2_CONSTANTS.WEIGHT_MAX"
                                :precision="CHARACTER2_CONSTANTS.WEIGHT_PRECISION"
                                @change="handleWeightChange"
                            />
                        </FormField>
                    </div>
                </template>
            </b-table>

            <div v-if="weightSumError" class="error-tip">
                {{ store.weightValidationState.errorMessage || '权重和必须等于 1' }}
            </div>

            <!-- 团队角色常模 -->
            <div class="section-title">
                <h5>团队角色常模</h5>
            </div>

            <b-table border fitWidth :columns="normColumns" :tableData="normTableData">
                <template v-for="(col, index) in normColumns" :key="col.field" #[`th-${col.field}`]>
                    <div v-if="index === 0" class="reference">
                        <div style="white-space: nowrap">团队角色名称</div>
                    </div>
                    <div v-else-if="index === 1" class="reference">
                        <div style="white-space: nowrap">常模平均分</div>
                    </div>
                    <div v-else class="reference">
                        <div style="white-space: nowrap">常模标准差</div>
                    </div>
                </template>
                <template v-for="(col, index) in normColumns" :key="col.field" #[`td-${col.field}`]="{ raw, $index }">
                    <div v-if="index === 0">
                        {{ raw.groupName }}
                    </div>
                    <div v-else-if="index === 1" class="center">
                        <FormField :field="`normTableData.${$index}.avgScore`" hideLabel :rules="getNormRules('avgScore')">
                            <b-input-number
                                v-model="raw.avgScore"
                                style="width: 100%"
                                placeholder="请填写0-1000内数值，最多4位小数"
                                hideButton
                                :min="CHARACTER2_CONSTANTS.NORM_MIN"
                                :max="CHARACTER2_CONSTANTS.NORM_MAX"
                                :precision="CHARACTER2_CONSTANTS.NORM_PRECISION"
                            />
                        </FormField>
                    </div>
                    <div v-else class="center">
                        <FormField :field="`normTableData.${$index}.stdDev`" hideLabel :rules="getNormRules('stdDev')">
                            <b-input-number
                                v-model="raw.stdDev"
                                placeholder="请填写1000以内正数，最多4位小数"
                                hideButton
                                :min="CHARACTER2_CONSTANTS.NORM_STD_DEV_MIN"
                                :max="CHARACTER2_CONSTANTS.NORM_MAX"
                                :precision="CHARACTER2_CONSTANTS.NORM_PRECISION"
                            />
                        </FormField>
                    </div>
                </template>
            </b-table>
        </b-form>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import type { _FormComponent } from '@boss/design';
import { FormField } from '@crm/web-components';
import type { ApiConfigItem } from '../type';
import { CHARACTER2_CONSTANTS } from '../type';
import { useCharacter2Store } from '../store';
import { useCharacter2Form } from '../composables/useCharacter2Form';
import { useCharacter2Validation } from '../composables/useCharacter2Validation';
import { buildTeamRoleListFromFormData } from '../api';

const props = defineProps<{
    modelValue: ApiConfigItem[];
}>();

const emit = defineEmits<{
    'update:modelValue': [value: ApiConfigItem[]];
    validate: [callback: () => Promise<boolean>];
}>();

// 使用 store 和组合式函数
const store = useCharacter2Store();
const formRef = ref<_FormComponent>();

// 防止循环更新的标志
const isInitializing = ref(false);

// 使用表单逻辑组合式函数
const { configGroups, weightTableData, normTableData, weightColumns, normColumns, weightSumError, validateWeightSum, initFromApiData, getCurrentFormData, updateColumns } =
    useCharacter2Form();

// 使用验证组合式函数
const { validateForm, getWeightRules, getNormRules, getConfigGroupNameRules } = useCharacter2Validation(formRef);

// 处理权重变化
function handleWeightChange() {
    validateWeightSum();
    // 只有在非初始化状态下才发送数据到父组件
    if (!isInitializing.value) {
        emitFormData();
    }
}

// 发送表单数据到父组件
function emitFormData() {
    // 防止在初始化期间发送数据
    if (isInitializing.value) {
        return;
    }

    const formData = getCurrentFormData();
    const apiData = buildTeamRoleListFromFormData(formData);
    emit('update:modelValue', apiData);
}

// 暴露校验方法和数据获取方法给父组件
defineExpose({
    validate: validateForm,
    getCurrentFormData,
});

// 监听父组件传入的数据变化
watch(
    () => props.modelValue,
    (newValue, oldValue) => {
        // 避免在初始化期间或数据相同时重复处理
        if (isInitializing.value) {
            return;
        }

        if (Array.isArray(newValue) && newValue.length > 0) {
            // 简单的深度比较，避免不必要的更新
            if (JSON.stringify(newValue) !== JSON.stringify(oldValue)) {
                initFromApiDataWithTeamRoles(newValue);
            }
        }
    },
    { immediate: true, deep: true }
);

// 监听内部数据变化，向父组件发送更新
watch(
    [weightTableData, normTableData],
    () => {
        // 只有在非初始化状态下才发送数据到父组件
        if (!isInitializing.value) {
            emitFormData();
        }
    },
    { deep: true }
);

// 团队角色专用的数据初始化函数
function initFromApiDataWithTeamRoles(apiData: ApiConfigItem[]) {
    // 设置初始化标志，防止循环更新
    isInitializing.value = true;

    try {
        // 确保团队角色数据补齐到9个
        const completeApiData = ensureCompleteTeamRoles(apiData);
        initFromApiData(completeApiData);
    } finally {
        // 确保标志被重置
        isInitializing.value = false;
    }
}

// 确保团队角色数据补齐到9个
function ensureCompleteTeamRoles(apiData: ApiConfigItem[]): ApiConfigItem[] {
    if (!Array.isArray(apiData) || apiData.length === 0) {
        // 如果没有数据，创建默认的团队角色数据
        return [
            {
                encId: '1',
                headName: '团队角色',
                normalAverageScore: 0,
                normalStandardDeviation: 0,
                rowDataList: CHARACTER2_CONSTANTS.DEFAULT_TEAM_ROLES.map((name, index) => ({
                    encDimensionId: (index + 1).toString(),
                    dimensionName: name,
                    define: null,
                    weight: 0,
                })),
            },
        ];
    }

    // 处理现有数据，确保补齐到9个团队角色
    const result = [...apiData];
    const firstItem = result[0];

    if (firstItem && firstItem.rowDataList) {
        const existingRoles = firstItem.rowDataList;
        const existingRoleNames = existingRoles.map((role) => role.dimensionName);

        // 找出缺失的团队角色
        const missingRoles = CHARACTER2_CONSTANTS.DEFAULT_TEAM_ROLES.filter((roleName) => !existingRoleNames.includes(roleName));

        // 补齐缺失的团队角色
        missingRoles.forEach((roleName, index) => {
            const newId = (existingRoles.length + index + 1).toString();
            existingRoles.push({
                encDimensionId: newId,
                dimensionName: roleName,
                define: null,
                weight: 0,
            });
        });

        // 确保总共有9个角色
        firstItem.rowDataList = existingRoles.slice(0, 9);
    }

    return result;
}

// 组件挂载时初始化团队角色数据
onMounted(async () => {
    // 设置初始化标志
    isInitializing.value = true;

    try {
        // 设置为团队角色类型的列配置，固定一个配置组
        configGroups.value = [{ id: '1', name: '团队角色' }];

        // 初始化团队角色的维度数据
        initTeamRoleDimensions();

        updateColumns(); // 使用默认参数
    } finally {
        // 重置初始化标志
        isInitializing.value = false;
    }
});

// 初始化团队角色维度数据
function initTeamRoleDimensions() {
    // 创建团队角色的权重表数据
    weightTableData.value = CHARACTER2_CONSTANTS.DEFAULT_TEAM_ROLES.map((roleName, index) => ({
        dimensionName: roleName,
        encryptDimensionId: (index + 1).toString(),
        '1': 0, // 配置组1的权重值
    }));

    // 创建团队角色的常模表数据
    normTableData.value = [
        {
            groupId: '1',
            groupName: '团队角色',
            avgScore: 0,
            stdDev: 0,
        },
    ];
}
</script>

<style lang="less" scoped>
.team-role-config {
    margin: 20px 0;
}

.sub-title {
    margin: 20px 0 10px 0;

    h4 {
        font-size: 14px;
        font-weight: 600;
        color: #1d2129;
        margin: 0;
    }
}

.section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 15px 0 10px 0;

    h5 {
        font-size: 13px;
        font-weight: 500;
        color: #1d2129;
        margin: 0;
    }
}

.error-tip {
    color: #f53f3f;
    font-size: 12px;
    margin-top: 5px;
}

.reference {
    gap: var(--size-3);
    .w-full;
    .group;
}

.center {
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
